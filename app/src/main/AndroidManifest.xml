<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/Theme.Practise"
        tools:targetApi="31">
        <activity
            android:name=".biorhythm.BiorhythmActivity"
            android:exported="true" />
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".exo.ExoPlayerActivity"
            android:exported="true" />
        <activity
            android:name=".insets.WindowInsetsActivity"
            android:exported="true" />
        <activity
            android:name=".data.DataStoreActivity"
            android:exported="true" />
        <activity
            android:name=".event.EventActivity"
            android:exported="true" />
        <activity
            android:name=".database.DatabaseActivity"
            android:exported="true" />
        <activity
            android:name=".recycler.RecyclerActivity"
            android:exported="true" />
        <activity
            android:name=".proxy.ProxyActivity"
            android:exported="true" />
        <activity
            android:name=".compress.CompressActivity"
            android:exported="true" />
        <activity
            android:name="com.xaluoqone.practise.compress.BitmapMemoryManagerActivity"
            android:exported="true" />
        <activity
            android:name="com.xaluoqone.practise.large.LargeImageActivity"
            android:exported="true" />
        <activity
            android:name="com.xaluoqone.practise.compose.ComposeActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize" />
    </application>
</manifest>