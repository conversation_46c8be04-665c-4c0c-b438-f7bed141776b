package com.xaluoqone.practise.compose

import android.content.ComponentCallbacks
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Color
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import com.xaluoqone.practise.compose.nav.AppNav
import com.xaluoqone.practise.compose.theme.AppTheme
import com.xaluoqone.practise.ex.setDensity
import kotlin.properties.Delegates

class ComposeActivity : ComponentActivity() {
    private var currentOrientation by Delegates.observable(-1) { _, old, new ->
        if (old != new) setDensity()
    }

    private val callback = object : ComponentCallbacks {
        override fun onConfigurationChanged(newConfig: Configuration) {
            currentOrientation = newConfig.orientation
        }

        override fun onLowMemory() {

        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        application.registerComponentCallbacks(callback)

        var statusBarHeightPx by mutableIntStateOf(0)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = true
            isAppearanceLightNavigationBars = true
        }
        window.statusBarColor = Color.TRANSPARENT
        window.navigationBarColor = Color.TRANSPARENT
        ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { _, insets ->
            val statusBarInsets = insets.getInsets(WindowInsetsCompat.Type.statusBars())
            statusBarHeightPx = statusBarInsets.top
            insets
        }
        setContent {
            AppTheme {
                val density = LocalDensity.current
                val statusBarHeight by remember(density) {
                    derivedStateOf { with(density) { statusBarHeightPx.toDp() } }
                }
                CompositionLocalProvider(LocalStatusBarHeight provides statusBarHeight) {
                    AppNav()
                }
            }
        }
    }

    override fun getResources(): Resources? {
        val resources = super.getResources()
        resources.setDensity()
        return createConfigurationContext(resources.configuration).resources
    }

    override fun onDestroy() {
        super.onDestroy()
        application.unregisterComponentCallbacks(callback)
    }
}

val LocalStatusBarHeight = staticCompositionLocalOf { 0.dp }