package com.xaluoqone.practise.compose.page

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation3.runtime.NavKey
import com.xaluoqone.practise.compose.LocalStatusBarHeight
import com.xaluoqone.practise.compose.nav.AnimatedVisibility
import com.xaluoqone.practise.compose.nav.Animation
import com.xaluoqone.practise.compose.nav.Custom
import com.xaluoqone.practise.compose.nav.SideEffect
import com.xaluoqone.practise.compose.nav.Transition
import com.xaluoqone.practise.compose.nav.Usage

val chapters = listOf(
    Usage to "Compose简单用法",
    Custom to "自定义Composable",
    Animation to "Compose动画",
    Transition to "Compose Transition 动画",
    AnimatedVisibility to "AnimatedVisibility",
    SideEffect to "SideEffect",
)

@Composable
fun MainPage(navTo: (NavKey) -> Unit) {
    LazyColumn(Modifier.padding(top = LocalStatusBarHeight.current)) {
        items(chapters) {
            TextButton(
                onClick = {
                    navTo(it.first)
                },
                Modifier
                    .padding(horizontal = 6.dp)
                    .fillMaxWidth()
            ) {
                Text(it.second)
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MainPagePreview() {
    MainPage {}
}