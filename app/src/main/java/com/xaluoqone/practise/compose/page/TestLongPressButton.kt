package com.xaluoqone.practise.compose.page

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

/**
 * 测试修复后的 LongPressInteractiveButton 的简化版本
 */
@Preview
@Composable
fun TestLongPressButton() {
    val context = LocalContext.current
    
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        // 显示扇形形状测试
        Box(
            modifier = Modifier
                .size(240.dp, 120.dp)
                .background(Color.Gray.copy(alpha = 0.3f), SectorShape)
        ) {
            // 左侧圆形
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset(x = (-120 * 0.6f).dp, y = (-120 * 0.6f).dp)
                    .size(80.dp)
                    .background(Color.Red.copy(alpha = 0.7f), CircleShape)
            ) {
                Text("左", modifier = Modifier.align(Alignment.Center), color = Color.White)
            }
            
            // 右侧圆形
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset(x = (120 * 0.6f).dp, y = (-120 * 0.6f).dp)
                    .size(80.dp)
                    .background(Color.Blue.copy(alpha = 0.7f), CircleShape)
            ) {
                Text("右", modifier = Modifier.align(Alignment.Center), color = Color.White)
            }
        }
        
        // 测试按钮
        Button(
            onClick = {
                Toast.makeText(context, "扇形形状测试", Toast.LENGTH_SHORT).show()
            },
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 32.dp)
        ) {
            Text("测试扇形")
        }
    }
}
