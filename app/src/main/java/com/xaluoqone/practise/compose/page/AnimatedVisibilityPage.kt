package com.xaluoqone.practise.compose.page

import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.updateTransition
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

@Preview
@Composable
fun AnimatedVisibilityPage() {
    val context = LocalContext.current
    Box(
        Modifier
            .fillMaxSize()
            .clickable { }) {
        LongPressInteractiveButton(
            Modifier.align(Alignment.BottomCenter),
            buttonContent = { Text("按住", color = Color.White) },
            onLeftAction = {
                Toast.makeText(context, "发送语音！", Toast.LENGTH_SHORT).show()
                // 在这里执行发送语音的逻辑
            },
            onRightAction = {
                Toast.makeText(context, "取消语音！", Toast.LENGTH_SHORT).show()
                // 在这里执行取消语音的逻辑
            },
            onTap = {
                Toast.makeText(context, "短按事件触发！", Toast.LENGTH_SHORT).show()
                // 在这里执行短按事件的逻辑
            }
        )
    }
}

// 定义动画状态，用于控制半圆形和圆形视图的显示/隐藏
enum class OverlayState {
    Hidden, // 隐藏状态
    Visible // 显示状态
}

// 现代化的扇形形状 - 创建一个更宽的扇形，类似语音消息界面
val ModernSectorShape = GenericShape { size: Size, _: LayoutDirection ->
    val centerX = size.width / 2f
    val centerY = size.height // 扇形的圆心在底部中心
    val radius = size.height // 半径等于高度

    // 扇形角度：从210度到330度（底部向上120度扇形）
    val startAngle = 210f * PI / 180f // 转换为弧度
    val sweepAngle = 120f // 120度扇形，更宽的角度

    // 移动到圆心
    moveTo(centerX, centerY)

    // 画到扇形起始点
    val startX = centerX + radius * cos(startAngle).toFloat()
    val startY = centerY + radius * sin(startAngle).toFloat()
    lineTo(startX, startY)

    // 画扇形弧线
    arcTo(
        rect = Rect(
            centerX - radius,
            centerY - radius,
            centerX + radius,
            centerY + radius
        ),
        startAngleDegrees = 210f,
        sweepAngleDegrees = sweepAngle,
        forceMoveTo = false
    )

    // 回到圆心
    lineTo(centerX, centerY)
    close()
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
fun LongPressInteractiveButton(
    modifier: Modifier = Modifier,
    buttonContent: @Composable () -> Unit,
    onLeftAction: () -> Unit,
    onRightAction: () -> Unit,
    onTap: () -> Unit, // 短按事件
    overlayRadius: Dp = 120.dp, // 半圆形半径
    targetCircleRadius: Dp = 40.dp // 左右圆形目标半径
) {
    val context = LocalContext.current
    val density = LocalDensity.current
    val overlayRadiusPx = with(density) { overlayRadius.toPx() }
    val targetCircleRadiusPx = with(density) { targetCircleRadius.toPx() }

    var overlayState by remember { mutableStateOf(OverlayState.Hidden) }
    val transition = updateTransition(overlayState, label = "overlayTransition")

    // 半圆形缩放动画
    val overlayScale by transition.animateFloat(
        transitionSpec = { spring(dampingRatio = Spring.DampingRatioMediumBouncy) },
        label = "overlayScale"
    ) { state ->
        if (state == OverlayState.Visible) 1f else 0f
    }

    // 左右圆形透明度动画
    val targetAlpha by transition.animateFloat(
        transitionSpec = { tween(durationMillis = 200) },
        label = "targetAlpha"
    ) { state ->
        if (state == OverlayState.Visible) 1f else 0f
    }

    var isLongPressDetected by remember { mutableStateOf(false) }
    var currentTouchPosition by remember { mutableStateOf(Offset.Unspecified) }

    val coroutineScope = rememberCoroutineScope()
    var longPressJob: Job? by remember { mutableStateOf(null) }



    Box(
        modifier = modifier.fillMaxSize()
    ) {

        // 主按钮 (作为长按的触发区域)
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 32.dp)
                .size(80.dp)
                .background(
                    if (isLongPressDetected) Color.Red.copy(alpha = 0.8f) else Color.Blue,
                    CircleShape
                )
                .pointerInput(Unit) {
                    awaitEachGesture {
                        val initialDown = awaitFirstDown()
                        currentTouchPosition = initialDown.position

                        // 启动长按检测
                        longPressJob = coroutineScope.launch {
                            delay(200) // 200ms长按阈值
                            isLongPressDetected = true
                            overlayState = OverlayState.Visible
                        }

                        // 等待手指抬起
                        var currentEvent = initialDown
                        do {
                            currentEvent = awaitPointerEvent().changes.first()
                            currentTouchPosition = currentEvent.position
                        } while (currentEvent.pressed)

                        longPressJob?.cancel()

                        if (isLongPressDetected) {
                            // 简化的区域检测 - 基于触摸位置的相对偏移
                            val touchOffsetX = currentTouchPosition.x - initialDown.position.x
                            val touchOffsetY = currentTouchPosition.y - initialDown.position.y

                            // 检测是否拖拽到左侧或右侧区域
                            if (touchOffsetX < -50 && touchOffsetY < -50) {
                                // 拖拽到左上方 - 发送
                                onLeftAction()
                            } else if (touchOffsetX > 50 && touchOffsetY < -50) {
                                // 拖拽到右上方 - 取消
                                onRightAction()
                            }

                            overlayState = OverlayState.Hidden
                            isLongPressDetected = false
                        } else {
                            // 短按
                            onTap()
                        }
                    }
                },
            contentAlignment = Alignment.Center
        ) {
            buttonContent()
        }

        // 扇形叠加层
        AnimatedVisibility(
            visible = overlayState == OverlayState.Visible,
            Modifier
                .align(Alignment.BottomCenter),
            enter = scaleIn(animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)),
            exit = scaleOut(animationSpec = tween(durationMillis = 200))
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset(y = (-32).dp) // 向上偏移，使其出现在按钮上方
                    .size(overlayRadius * 2, overlayRadius) // 宽高
                    .graphicsLayer {
                        scaleX = overlayScale
                        scaleY = overlayScale
                        transformOrigin =
                            androidx.compose.ui.graphics.TransformOrigin(0.5f, 1f) // 从底部中心放大
                    }
                    .background(Color.Black.copy(alpha = 0.7f), ModernSectorShape)
            ) {
                // 左侧目标区域 - 发送语音
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .offset(x = -overlayRadius * 0.5f, y = -overlayRadius * 0.7f)
                        .size(targetCircleRadius * 2)
                        .background(Color.Green.copy(alpha = 0.9f), CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        "发送",
                        color = Color.White,
                        fontSize = 12.sp
                    )
                }

                // 右侧目标区域 - 取消
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .offset(x = overlayRadius * 0.5f, y = -overlayRadius * 0.7f)
                        .size(targetCircleRadius * 2)
                        .background(Color.Red.copy(alpha = 0.9f), CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        "取消",
                        color = Color.White,
                        fontSize = 12.sp
                    )
                }

                // 中央提示文字
                Text(
                    "拖拽到目标区域",
                    modifier = Modifier
                        .align(Alignment.Center)
                        .offset(y = -overlayRadius * 0.3f),
                    color = Color.White,
                    fontSize = 14.sp
                )
            }
        }
    }
}

// 辅助函数：判断点是否在圆形内
fun isPointInCircle(point: Offset, circleCenter: Offset, circleRadius: Float): Boolean {
    val distance = sqrt((point.x - circleCenter.x).pow(2) + (point.y - circleCenter.y).pow(2))
    return distance <= circleRadius
}


// 扩展函数，用于方便计算
private fun Float.pow(n: Int): Float {
    return Math.pow(this.toDouble(), n.toDouble()).toFloat()
}