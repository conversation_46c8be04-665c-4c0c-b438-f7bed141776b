package com.xaluoqone.practise.compose.page

import android.R.attr.alpha
import android.R.attr.scaleY
import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.updateTransition
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.drag
import androidx.compose.foundation.gestures.waitForUpOrCancellation
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.PointerEventPass
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.sqrt

@Preview
@Composable
fun AnimatedVisibilityPage() {
    val context = LocalContext.current
    Box(
        Modifier
            .fillMaxSize()
            .clickable { }) {
        LongPressInteractiveButton(
            Modifier.align(Alignment.BottomCenter),
            buttonContent = { Text("按住") },
            onLeftAction = {
                Toast.makeText(context, "左侧事件触发！", Toast.LENGTH_SHORT).show()
                // 在这里执行左侧事件的逻辑
            },
            onRightAction = {
                Toast.makeText(context, "右侧事件触发！", Toast.LENGTH_SHORT).show()
                // 在这里执行右侧事件的逻辑
            },
            onTap = {
                Toast.makeText(context, "短按事件触发！", Toast.LENGTH_SHORT).show()
                // 在这里执行短按事件的逻辑
            }
        )
    }
}

// 定义动画状态，用于控制半圆形和圆形视图的显示/隐藏
enum class OverlayState {
    Hidden, // 隐藏状态
    Visible // 显示状态
}

// 半圆形的形状
val HalfCircleShape = GenericShape { size: Size, _: LayoutDirection ->
    arcTo(
        rect = Rect(Offset(0f, 0f), size),
        startAngleDegrees = 180f,
        sweepAngleDegrees = 180f,
        forceMoveTo = false
    )
    lineTo(0f, size.height)
    lineTo(size.width, size.height)
    close()
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
fun LongPressInteractiveButton(
    modifier: Modifier = Modifier,
    buttonContent: @Composable () -> Unit,
    onLeftAction: () -> Unit,
    onRightAction: () -> Unit,
    onTap: () -> Unit, // 短按事件
    overlayRadius: Dp = 120.dp, // 半圆形半径
    targetCircleRadius: Dp = 40.dp // 左右圆形目标半径
) {
    val context = LocalContext.current
    val density = LocalDensity.current
    val overlayRadiusPx = with(density) { overlayRadius.toPx() }
    val targetCircleRadiusPx = with(density) { targetCircleRadius.toPx() }

    var overlayState by remember { mutableStateOf(OverlayState.Hidden) }
    val transition = updateTransition(overlayState, label = "overlayTransition")

    // 半圆形缩放动画
    val overlayScale by transition.animateFloat(
        transitionSpec = { spring(dampingRatio = Spring.DampingRatioMediumBouncy) },
        label = "overlayScale"
    ) { state ->
        if (state == OverlayState.Visible) 1f else 0f
    }

    // 左右圆形透明度动画
    val targetAlpha by transition.animateFloat(
        transitionSpec = { tween(durationMillis = 200) },
        label = "targetAlpha"
    ) { state ->
        if (state == OverlayState.Visible) 1f else 0f
    }

    var isLongPressDetected by remember { mutableStateOf(false) }
    var currentTouchPosition by remember { mutableStateOf(Offset.Unspecified) }

    val coroutineScope = rememberCoroutineScope()
    var longPressJob: Job? by remember { mutableStateOf(null) }

    // 左右目标圆心的计算
    // 假设半圆形的圆心在底部按钮的中心上方 overlayRadius 处
    val leftTargetCenter = remember(overlayRadiusPx) { Offset(-overlayRadiusPx / 2, -overlayRadiusPx / 2) }
    val rightTargetCenter = remember(overlayRadiusPx) { Offset(overlayRadiusPx / 2, -overlayRadiusPx / 2) }

    Box(
        modifier = modifier
            .fillMaxSize() // 填充父级以捕获整个屏幕的触摸
            .pointerInput(Unit) {
                awaitEachGesture {
                    val initialDown = awaitFirstDown(pass = PointerEventPass.Initial) // 获取初始按下事件
                    val initialDownTime = initialDown.uptimeMillis // 记录按下时间
                    currentTouchPosition = initialDown.position // 更新当前触摸位置

                    longPressJob = coroutineScope.launch {
                        delay(200) // 假设200ms为长按阈值
                        if (initialDown.isConsumed.not()) { // 如果事件未被消费，说明是长按
                            isLongPressDetected = true
                            overlayState = OverlayState.Visible
                        }
                    }

                    // 拖动手势
                    val dragResult = drag(initialDown.id) { change ->
                        currentTouchPosition = change.position // 更新拖动时的触摸位置
                        change.consume() // 消费事件
                    }

                    // 手指抬起或取消
                    val upOrCancellation = if (dragResult != null) {
                        // 如果有拖动，则拖动完成后会返回 up或cancellation
                        dragResult // dragResult 是 PointerInputChange
                    } else {
                        // 没有拖动，则等待抬起或取消
                        waitForUpOrCancellation()?.isConsumed ?: false
                    }

                    longPressJob?.cancel() // 取消长按检测

                    if (isLongPressDetected) {
                        // 在此处理松手事件，判断是否在左右圆形区域内
                        val relativePosition = currentTouchPosition - initialDown.position // 相对于初始按下位置的偏移
                        val currentPointerX = initialDown.position.x + relativePosition.x
                        val currentPointerY = initialDown.position.y + relativePosition.y

                        // 将屏幕坐标转换为相对于半圆形容器中心的坐标
                        // 假设半圆形容器的中心在屏幕中心（或者根据你实际布局的锚点调整）
                        // 这里为了简化，我们假设整个Box的中心是半圆形的原点
                        val buttonCenter = Offset(size.width / 2f, size.height / 2f)
                        val touchRelativeButton = currentTouchPosition - buttonCenter

                        val isInLeftCircle = isPointInCircle(touchRelativeButton, leftTargetCenter, targetCircleRadiusPx)
                        val isInRightCircle = isPointInCircle(touchRelativeButton, rightTargetCenter, targetCircleRadiusPx)

                        if (isInLeftCircle) {
                            onLeftAction()
                            Toast.makeText(context, "触发左侧事件", Toast.LENGTH_SHORT).show()
                        } else if (isInRightCircle) {
                            onRightAction()
                            Toast.makeText(context, "触发右侧事件", Toast.LENGTH_SHORT).show()
                        }
                        // 动画收起
                        overlayState = OverlayState.Hidden
                        isLongPressDetected = false
                    } else {
                        // 如果不是长按，则认为是短按
                        val upTime = (waitForUpOrCancellation()?.uptimeMillis ?: initialDown.uptimeMillis)
                        if (upTime - initialDownTime < 200) { // 短按阈值，需要与长按阈值匹配
                            onTap()
                        }
                    }
                    isLongPressDetected = false // 重置状态
                }
            }
    ) {
        // 主按钮 (作为长按的触发区域)
        Button(
            onClick = { /* 短按事件已经在pointerInput中处理 */ },
            modifier = Modifier
                .align(Alignment.BottomCenter) // 假设按钮在底部
                .padding(bottom = 32.dp)
                .size(80.dp),
            enabled = !isLongPressDetected // 长按时禁用点击
        ) {
            buttonContent()
        }

        // 半圆形叠加层
        AnimatedVisibility(
            visible = overlayState == OverlayState.Visible,
            enter = scaleIn(animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)),
            exit = scaleOut(animationSpec = tween(durationMillis = 200))
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset(y = (-32).dp) // 向上偏移，使其出现在按钮上方
                    .size(overlayRadius * 2, overlayRadius) // 宽高
                    .graphicsLayer {
                        scaleX = overlayScale
                        scaleY = overlayScale
                        transformOrigin = androidx.compose.ui.graphics.TransformOrigin(0.5f, 1f) // 从底部中心放大
                    }
                    .background(Color.Gray.copy(alpha = 0.8f), HalfCircleShape)
            ) {
                // 左侧圆形目标
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter) // 相对于半圆形容器底部居中
                        .offset(x = -overlayRadius / 2, y = -overlayRadius / 2) // 左移并上移
                        .size(targetCircleRadius * 2)
                        .graphicsLayer { alpha = targetAlpha }
                        .background(Color.Red.copy(alpha = 0.7f), CircleShape)
                ) {
                    Text("左", modifier = Modifier.align(Alignment.Center), color = Color.White)
                }

                // 右侧圆形目标
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter) // 相对于半圆形容器底部居中
                        .offset(x = overlayRadius / 2, y = -overlayRadius / 2) // 右移并上移
                        .size(targetCircleRadius * 2)
                        .graphicsLayer { alpha = targetAlpha }
                        .background(Color.Blue.copy(alpha = 0.7f), CircleShape)
                ) {
                    Text("右", modifier = Modifier.align(Alignment.Center), color = Color.White)
                }
            }
        }
    }
}

// 辅助函数：判断点是否在圆形内
fun isPointInCircle(point: Offset, circleCenter: Offset, circleRadius: Float): Boolean {
    val distance = sqrt((point.x - circleCenter.x).pow(2) + (point.y - circleCenter.y).pow(2))
    return distance <= circleRadius
}

// 扩展函数，用于方便计算
private fun Float.pow(n: Int): Float {
    return Math.pow(this.toDouble(), n.toDouble()).toFloat()
}