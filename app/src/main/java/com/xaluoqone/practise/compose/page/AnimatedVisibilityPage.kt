package com.xaluoqone.practise.compose.page

import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.updateTransition
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.PointerInputChange
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

@Preview
@Composable
fun AnimatedVisibilityPage() {
    val context = LocalContext.current
    Box(
        Modifier
            .fillMaxSize()
            .clickable { }) {
        LongPressInteractiveButton(
            Modifier
                .fillMaxWidth()
                .height(300.dp)
                .align(Alignment.BottomCenter),
            buttonContent = { Text("按住") },
            onLeftAction = {
                Toast.makeText(context, "左侧事件触发！", Toast.LENGTH_SHORT).show()
                // 在这里执行左侧事件的逻辑
            },
            onRightAction = {
                Toast.makeText(context, "右侧事件触发！", Toast.LENGTH_SHORT).show()
                // 在这里执行右侧事件的逻辑
            },
            onTap = {
                Toast.makeText(context, "短按事件触发！", Toast.LENGTH_SHORT).show()
                // 在这里执行短按事件的逻辑
            }
        )
    }
}

// 定义动画状态，用于控制半圆形和圆形视图的显示/隐藏
enum class OverlayState {
    Hidden, // 隐藏状态
    Visible // 显示状态
}

// 扇形的形状 - 创建一个120度的扇形，从底部中心向上展开
val SectorShape = GenericShape { size: Size, _: LayoutDirection ->
    val centerX = size.width / 2f
    val centerY = size.height // 扇形的圆心在底部中心
    val radius = size.height // 半径等于高度

    // 扇形角度：从225度到315度（底部向上120度扇形）
    val startAngle = 225f * PI / 180f // 转换为弧度
    val endAngle = 315f * PI / 180f
    val sweepAngle = 90f // 90度扇形

    // 移动到圆心
    moveTo(centerX, centerY)

    // 画到扇形起始点
    val startX = centerX + radius * cos(startAngle).toFloat()
    val startY = centerY + radius * sin(startAngle).toFloat()
    lineTo(startX, startY)

    // 画扇形弧线
    arcTo(
        rect = Rect(
            centerX - radius,
            centerY - radius,
            centerX + radius,
            centerY + radius
        ),
        startAngleDegrees = 225f,
        sweepAngleDegrees = sweepAngle,
        forceMoveTo = false
    )

    // 回到圆心
    lineTo(centerX, centerY)
    close()
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
fun LongPressInteractiveButton(
    modifier: Modifier = Modifier,
    buttonContent: @Composable () -> Unit,
    onLeftAction: () -> Unit,
    onRightAction: () -> Unit,
    onTap: () -> Unit, // 短按事件
    overlayRadius: Dp = 120.dp, // 半圆形半径
    targetCircleRadius: Dp = 40.dp // 左右圆形目标半径
) {
    val context = LocalContext.current
    val density = LocalDensity.current
    val overlayRadiusPx = with(density) { overlayRadius.toPx() }
    val targetCircleRadiusPx = with(density) { targetCircleRadius.toPx() }

    var overlayState by remember { mutableStateOf(OverlayState.Hidden) }
    val transition = updateTransition(overlayState, label = "overlayTransition")

    // 半圆形缩放动画
    val overlayScale by transition.animateFloat(
        transitionSpec = { spring(dampingRatio = Spring.DampingRatioMediumBouncy) },
        label = "overlayScale"
    ) { state ->
        if (state == OverlayState.Visible) 1f else 0f
    }

    // 左右圆形透明度动画
    val targetAlpha by transition.animateFloat(
        transitionSpec = { tween(durationMillis = 200) },
        label = "targetAlpha"
    ) { state ->
        if (state == OverlayState.Visible) 1f else 0f
    }

    var isLongPressDetected by remember { mutableStateOf(false) }
    var currentTouchPosition by remember { mutableStateOf(Offset.Unspecified) }

    val coroutineScope = rememberCoroutineScope()
    var longPressJob: Job? by remember { mutableStateOf(null) }

    // 左右目标圆心的计算 - 相对于扇形容器的坐标
    // 扇形容器的尺寸是 overlayRadius * 2 宽 x overlayRadius 高
    // 左右圆形应该位于扇形的两侧，距离扇形底部中心一定距离
    val leftTargetCenter = remember(overlayRadiusPx) {
        Offset(-overlayRadiusPx * 0.6f, -overlayRadiusPx * 0.6f)
    }
    val rightTargetCenter = remember(overlayRadiusPx) {
        Offset(overlayRadiusPx * 0.6f, -overlayRadiusPx * 0.6f)
    }

    Box(
        modifier = modifier.fillMaxSize()
    ) {
                awaitEachGesture {
                    val initialDown = awaitFirstDown() // 获取初始按下事件
                    val initialDownTime = initialDown.uptimeMillis // 记录按下时间
                    currentTouchPosition = initialDown.position // 更新当前触摸位置

                    // 启动长按检测
                    longPressJob = coroutineScope.launch {
                        delay(200) // 200ms长按阈值
                        isLongPressDetected = true
                        overlayState = OverlayState.Visible
                        Toast.makeText(context, "长按检测到！", Toast.LENGTH_SHORT).show()
                    }

                    // 等待手指抬起或移动
                    var currentEvent: PointerInputChange
                    do {
                        currentEvent = awaitPointerEvent().changes.first()
                        currentTouchPosition = currentEvent.position
                    } while (currentEvent.pressed)

                    longPressJob?.cancel() // 取消长按检测

                    if (isLongPressDetected) {
                        // 计算扇形容器在屏幕中的位置
                        // 扇形容器位于底部中心，向上偏移32dp，尺寸为overlayRadius*2 x overlayRadius
                        val buttonCenterX = size.width / 2f
                        val buttonCenterY =
                            size.height - with(density) { (32 + 40).dp.toPx() } // 按钮中心Y坐标

                        // 扇形容器的底部中心坐标（扇形的圆心）
                        val sectorCenterX = buttonCenterX
                        val sectorCenterY =
                            buttonCenterY - with(density) { 32.dp.toPx() } // 向上偏移32dp

                        // 将当前触摸位置转换为相对于扇形容器底部中心的坐标
                        val touchRelativeToSector = Offset(
                            currentTouchPosition.x - sectorCenterX,
                            currentTouchPosition.y - sectorCenterY
                        )

                        // 检查是否在左右圆形区域内
                        val isInLeftCircle = isPointInCircle(
                            touchRelativeToSector,
                            leftTargetCenter,
                            targetCircleRadiusPx
                        )
                        val isInRightCircle = isPointInCircle(
                            touchRelativeToSector,
                            rightTargetCenter,
                            targetCircleRadiusPx
                        )

                        if (isInLeftCircle) {
                            onLeftAction()
                        } else if (isInRightCircle) {
                            onRightAction()
                        }

                        // 动画收起
                        overlayState = OverlayState.Hidden
                        isLongPressDetected = false
                    } else {
                        // 如果不是长按，则认为是短按
                        Toast.makeText(context, "短按检测到！", Toast.LENGTH_SHORT).show()
                        onTap()
                    }
                    isLongPressDetected = false // 重置状态
                }
            }
    ) {
        // 主按钮 (作为长按的触发区域)
        Button(
            onClick = { /* 短按事件已经在pointerInput中处理 */ },
            modifier = Modifier
                .align(Alignment.BottomCenter) // 假设按钮在底部
                .padding(bottom = 32.dp)
                .size(80.dp),
            enabled = !isLongPressDetected // 长按时禁用点击
        ) {
            buttonContent()
        }

        // 扇形叠加层
        AnimatedVisibility(
            visible = overlayState == OverlayState.Visible,
            Modifier
                .align(Alignment.BottomCenter),
            enter = scaleIn(animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)),
            exit = scaleOut(animationSpec = tween(durationMillis = 200))
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset(y = (-32).dp) // 向上偏移，使其出现在按钮上方
                    .size(overlayRadius * 2, overlayRadius) // 宽高
                    .graphicsLayer {
                        scaleX = overlayScale
                        scaleY = overlayScale
                        transformOrigin =
                            androidx.compose.ui.graphics.TransformOrigin(0.5f, 1f) // 从底部中心放大
                    }
                    .background(Color.Gray.copy(alpha = 0.8f), SectorShape)
            ) {
                // 左侧圆形目标
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter) // 相对于扇形容器底部居中
                        .offset(x = -overlayRadius * 0.6f, y = -overlayRadius * 0.6f) // 调整位置以匹配扇形
                        .size(targetCircleRadius * 2)
                        .graphicsLayer { alpha = targetAlpha }
                        .background(Color.Red.copy(alpha = 0.7f), CircleShape)
                ) {
                    Text("左", modifier = Modifier.align(Alignment.Center), color = Color.White)
                }

                // 右侧圆形目标
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter) // 相对于扇形容器底部居中
                        .offset(x = overlayRadius * 0.6f, y = -overlayRadius * 0.6f) // 调整位置以匹配扇形
                        .size(targetCircleRadius * 2)
                        .graphicsLayer { alpha = targetAlpha }
                        .background(Color.Blue.copy(alpha = 0.7f), CircleShape)
                ) {
                    Text("右", modifier = Modifier.align(Alignment.Center), color = Color.White)
                }
            }
        }
    }
}

// 辅助函数：判断点是否在圆形内
fun isPointInCircle(point: Offset, circleCenter: Offset, circleRadius: Float): Boolean {
    val distance = sqrt((point.x - circleCenter.x).pow(2) + (point.y - circleCenter.y).pow(2))
    return distance <= circleRadius
}

// 扩展函数，用于方便计算
private fun Float.pow(n: Int): Float {
    return Math.pow(this.toDouble(), n.toDouble()).toFloat()
}