package com.xaluoqone.practise.compose.page

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 简化的长按测试组件，用于验证长按检测逻辑
 */
@Preview
@Composable
fun SimpleLongPressTest() {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    var isLongPressed by remember { mutableStateOf(false) }
    var longPressJob: Job? by remember { mutableStateOf(null) }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .size(200.dp, 100.dp)
                .background(
                    if (isLongPressed) Color.Red else Color.Blue,
                    RoundedCornerShape(8.dp)
                )
                .pointerInput(Unit) {
                    awaitEachGesture {
                        val down = awaitFirstDown()

                        // 启动长按检测
                        longPressJob = coroutineScope.launch {
                            delay(200) // 200ms长按阈值
                            isLongPressed = true
                            Toast.makeText(context, "长按检测成功！", Toast.LENGTH_SHORT).show()
                        }

                        // 等待手指抬起
                        var currentEvent = down
                        do {
                            currentEvent = awaitPointerEvent().changes.first()
                        } while (currentEvent.pressed)

                        // 取消长按检测
                        longPressJob?.cancel()

                        if (isLongPressed) {
                            Toast.makeText(context, "长按完成", Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(context, "短按", Toast.LENGTH_SHORT).show()
                        }

                        // 重置状态
                        isLongPressed = false
                    }
                },
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (isLongPressed) "长按中..." else "按住测试",
                color = Color.White
            )
        }
    }
}
