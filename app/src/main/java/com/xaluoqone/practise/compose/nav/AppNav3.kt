package com.xaluoqone.practise.compose.nav

import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.togetherWith
import androidx.compose.runtime.Composable
import androidx.navigation3.runtime.NavKey
import androidx.navigation3.runtime.entry
import androidx.navigation3.runtime.entryProvider
import androidx.navigation3.runtime.rememberNavBackStack
import androidx.navigation3.ui.NavDisplay
import com.xaluoqone.practise.compose.page.AnimatedVisibilityPage
import com.xaluoqone.practise.compose.page.AnimationPage
import com.xaluoqone.practise.compose.page.CustomPage
import com.xaluoqone.practise.compose.page.MainPage
import com.xaluoqone.practise.compose.page.SideEffectPage
import com.xaluoqone.practise.compose.page.SimpleUsagePage
import com.xaluoqone.practise.compose.page.TransitionPage
import kotlinx.serialization.Serializable

@Serializable
data object Main : NavKey

@Serializable
data object Usage : NavKey

@Serializable
data object Custom : NavKey

@Serializable
data object Animation : NavKey

@Serializable
data object Transition : NavKey

@Serializable
data object AnimatedVisibility : NavKey

@Serializable
data object SideEffect : NavKey

@Composable
fun AppNav() {
    val backStack = rememberNavBackStack(Main)

    NavDisplay(
        backStack = backStack,
        onBack = { backStack.removeLastOrNull() },
        entryProvider = entryProvider {
            entry<Main> {
                MainPage { backStack.add(it) }
            }
            entry<Usage> {
                SimpleUsagePage()
            }
            entry<Custom> {
                CustomPage()
            }
            entry<Animation> {
                AnimationPage()
            }
            entry<Transition> {
                TransitionPage()
            }
            entry<AnimatedVisibility> {
                AnimatedVisibilityPage()
            }
            entry<SideEffect> {
                SideEffectPage()
            }
        },
        transitionSpec = {
            slideInHorizontally(
                animationSpec = tween(1000),
                initialOffsetX = { it },
            ) togetherWith fadeOut(tween(1000))
        },
        popTransitionSpec = {
            fadeIn(tween(1000)) togetherWith
                    slideOutHorizontally(
                        animationSpec = tween(1000),
                        targetOffsetX = { it },
                    )
        },
        predictivePopTransitionSpec = {
            fadeIn(tween(1000)) togetherWith
                    slideOutHorizontally(
                        animationSpec = tween(1000),
                        targetOffsetX = { it },
                    )
        },
    )
}