package com.xaluoqone.practise.compose.page

import androidx.activity.compose.BackHandler
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xaluoqone.practise.ex.toDp
import com.xaluoqone.practise.ktx.logv
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun CustomPage() {
    val imeHeight = WindowInsets.ime.getBottom(LocalDensity.current).toDp
    var lastImeHeight by remember { mutableStateOf(imeHeight) }
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    val scrollState = rememberLazyListState()
    var showEmoji by remember { mutableStateOf(false) }
    var showIme by remember { mutableStateOf(imeHeight > 0.dp) }
    var imeState by remember { mutableStateOf(false) }
    var panelClosing by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()

    LaunchedEffect(Unit) {
        scrollState.scrollToItem(49) // 初始化时滚动到底部（假设有50条消息）
    }

    LaunchedEffect(imeHeight) {
        logv("键盘高度：${imeHeight}")
        showIme = imeHeight > 0.dp
        imeState = imeHeight > lastImeHeight
        lastImeHeight = imeHeight
        if (imeState && showEmoji) {
            showEmoji = false
        }
        //scrollState.scrollToItem(49)
    }

    BackHandler(showIme || showEmoji) {
        keyboardController?.hide()
        scope.launch {
            if (showEmoji) {
                panelClosing = true
                showEmoji = false
                delay(300)
                panelClosing = false
            }
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        LazyColumn(
            state = scrollState,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .background(Color(0XFFEEEEEE))
        ) {
            items(50) {
                Button(onClick = {}, Modifier.fillMaxWidth()) {
                    Text("btn-$it")
                }
            }
        }
        Row(
            Modifier
                .padding(horizontal = 16.dp, vertical = 10.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BasicTextField(
                "", {},
                Modifier
                    .weight(1f)
                    .focusRequester(focusRequester)
            )
            Spacer(Modifier.width(10.dp))
            Text(
                "😊",
                Modifier
                    .size(35.dp)
                    .clickable {
                        showEmoji = true
                        keyboardController?.hide()
                    },
                fontSize = 25.sp,
                textAlign = TextAlign.Center
            )
        }
        Box(
            Modifier
                .fillMaxWidth()
                .heightIn(imeHeight)
        ) {
            Box(
                Modifier
                    .fillMaxWidth()
                    .heightIn(imeHeight)
                    .animateContentSize(tween(if (imeState) 130 else if (showEmoji || panelClosing) 300 else 0))
            ) {
                if (showEmoji) {
                    LazyVerticalGrid(
                        GridCells.Fixed(8),
                        Modifier.fillMaxWidth(),
                        contentPadding = PaddingValues(horizontal = 16.dp)
                    ) {
                        items(64) {
                            Box(
                                Modifier
                                    .fillMaxWidth()
                                    .aspectRatio(1f)
                                    .clickable {},
                                contentAlignment = Alignment.Center
                            ) {
                                Text("😊")
                            }
                        }
                    }
                }
            }
        }
    }
}