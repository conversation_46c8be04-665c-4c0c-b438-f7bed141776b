package com.xaluoqone.practise.ex

import android.content.Context
import android.content.res.Resources
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.min
import kotlin.math.roundToInt

const val designWidth = 375f // 设计图屏幕的宽度

fun Context.setDensity() {
    resources?.setDensity()
    createConfigurationContext(resources.configuration)
}

fun Resources.setDensity() {
    val metrics = displayMetrics ?: return

    val width = metrics.widthPixels
    val height = metrics.heightPixels
    //获取以设计图总宽度下的density值
    val targetDensity = (min(width, height) / designWidth)
    //获取以设计图总宽度下的dpi值
    val targetDensityDpi = (160f * targetDensity).roundToInt()

    // 更新displayMetrics的信息
    metrics.density = targetDensity
    metrics.scaledDensity = targetDensity
    metrics.densityDpi = targetDensityDpi

    // 更新configuration的信息
    val configuration = configuration ?: return
    configuration.screenWidthDp = (width / targetDensity).roundToInt()
    configuration.screenHeightDp = (height / targetDensity).roundToInt()
    configuration.densityDpi = targetDensityDpi
    configuration.fontScale = 1f
}

fun Number.getActualPixel(res: Resources): Float {
    val displayMetrics = res.displayMetrics
    val width = displayMetrics.widthPixels
    val height = displayMetrics.heightPixels
    val density = (min(width, height) / designWidth)
    return toFloat() * density
}

// Compose 使用，返回单位为dp
val Number.dpx: Dp
    get() {
        val res = Resources.getSystem()
        return (getActualPixel(res) / res.displayMetrics.density).dp
    }

// Compose 使用，返回单位为sp
val Number.spx: TextUnit
    get() {
        val res = Resources.getSystem()
        return (getActualPixel(res) / res.displayMetrics.scaledDensity).sp
    }

// View系统使用，返回单位为px，如：1.dpn = 1设计图Size在屏幕上对应的像素数
val Number.dpn: Float
    get() = getActualPixel(Resources.getSystem())

// View系统使用，返回单位为px，如：1.spn = 1设计图Size在屏幕上对应的像素数
val Number.spn: Float
    get() = getActualPixel(Resources.getSystem())

val Number.dpi: Int
    get() = dpn.roundToInt()

val Number.spi: Int
    get() = spn.roundToInt()

val Dp.toPx: Float
    @Composable get() = LocalDensity.current.run {
        <EMAIL>()
    }

val Int.toDp: Dp
    @Composable get() = LocalDensity.current.run {
        <EMAIL>()
    }

val TextUnit.toPx: Float
    @Composable get() = LocalDensity.current.run {
        <EMAIL>()
    }