{"formatVersion": 1, "database": {"version": 2, "identityHash": "f0d7afadb4568bb0b3b27563d5a34a46", "entities": [{"tableName": "test_info", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `data` TEXT NOT NULL, `owner` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "data", "columnName": "data", "affinity": "TEXT", "notNull": true}, {"fieldPath": "owner", "columnName": "owner", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'f0d7afadb4568bb0b3b27563d5a34a46')"]}}