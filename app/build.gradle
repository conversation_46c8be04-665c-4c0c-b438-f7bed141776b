plugins {
    id "com.android.application"
    id "kotlin-android"
    id "com.google.devtools.ksp"
    id 'org.jetbrains.kotlin.plugin.compose'
    id 'org.jetbrains.kotlin.plugin.serialization'
}

android {
    namespace "com.xaluoqone.practise"
    compileSdk 36

    defaultConfig {
        applicationId "com.xaluoqone.practise"
        minSdk 26
        targetSdk 34
        versionCode 1
        versionName "1.0"

        ksp {
            arg("room.schemaLocation", "$projectDir/schemas")
        }

        externalNativeBuild {
            cmake {
                cppFlags ""
                abiFilters "arm64-v8a"
                arguments "-DANDROID_TOOLCHAIN=clang"
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
        }
    }

    compileOptions {
        sourceCompatibility java_version
        targetCompatibility java_version
    }

    kotlinOptions {
        jvmTarget = java_version
        freeCompilerArgs = ["-Xcontext-receivers"]
    }

    buildFeatures {
        buildConfig = true
        compose true
    }

    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.22.1"
        }
    }
}

dependencies {
    implementation platform('androidx.compose:compose-bom:2025.03.01')
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.foundation:foundation'
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.material:material-icons-core'
    implementation 'androidx.compose.material:material-icons-extended'
    implementation 'androidx.compose.material3:material3-window-size-class'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    debugImplementation 'androidx.compose.ui:ui-tooling'

    implementation 'androidx.activity:activity-compose:1.10.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.8.7'

    implementation "androidx.core:core-ktx:1.15.0"
    implementation "androidx.appcompat:appcompat:1.7.0"
    implementation "com.google.android.material:material:1.12.0"
    implementation "androidx.constraintlayout:constraintlayout:2.2.1"

    implementation "androidx.activity:activity-ktx:1.10.1"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.8.7"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7"

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.10.1"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1"

    implementation "com.google.android.exoplayer:exoplayer:2.19.1"

    implementation "androidx.datastore:datastore:1.1.4"
    implementation "androidx.datastore:datastore-preferences:1.1.4"

    implementation "com.jakewharton:disklrucache:2.0.2"

    api "androidx.room:room-ktx:2.6.1"
    ksp "androidx.room:room-compiler:2.6.1"

    implementation "com.squareup.okio:okio:3.10.2"

    implementation "androidx.navigation3:navigation3-runtime:1.0.0-alpha03"
    implementation "androidx.navigation3:navigation3-ui:1.0.0-alpha03"
    implementation "androidx.lifecycle:lifecycle-viewmodel-navigation3:1.0.0-alpha01"
}